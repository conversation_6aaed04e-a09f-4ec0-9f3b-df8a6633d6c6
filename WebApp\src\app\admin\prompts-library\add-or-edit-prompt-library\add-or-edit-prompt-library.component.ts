import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { WorkspaceServiceProxy, AgentDefinitionServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { AuthService } from '../../../../shared/services/auth.service';
import { Router } from '@angular/router';
@Component({
  selector: 'app-add-or-edit-prompt-library',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzModalModule,
    NzFormModule,
    NzInputModule,
    NzSwitchModule,
    NzButtonModule,
    NzSelectModule
  ],
  templateUrl: './add-or-edit-prompt-library.component.html',
  styleUrls: ['./add-or-edit-prompt-library.component.css']
})
export class AddOrEditPromptLibraryComponent implements OnInit {
  promptData: any = {
    id: 0,
    prompt: '',
    shortMessage: '',
    workspaceName: '',
    agentName: '',
  };
  workspaces: any[] = [];
  agents: any[] = [];
  workspaceName: string | undefined = '';
  constructor(
    private modalRef: NzModalRef,
    private workspaceService: WorkspaceServiceProxy,
    private agentService: AgentDefinitionServiceProxy,
    public authService: AuthService,
    private router: Router
  ) { }

  ngOnInit() {
    const data = this.modalRef.getConfig().nzData;
    if (data && data.promptData) {
      this.promptData = { ...data.promptData };
    }

    this.loadWorkspaces();
    this.loadAgents();
  }

  loadWorkspaces() {
    if (this.authService.isAdmin()) {
      this.workspaceService.getAll().subscribe((res: any) => {
        this.workspaces = res;
      });
    } else {
      this.workspaceService.getWorkspacesByUserEmail().subscribe((res: any) => {
        this.workspaces = res;
      });
    }
  }

  loadAgents() {
    this.agentService.getAllAgentName().subscribe((res: any) => {
      this.agents = res;
    });
  }

  onSubmit() {
    if (this.promptData.prompt) {
      // Validate that either workspace or agent is selected, but not both
      if (this.promptData.workspaceName && this.promptData.agentName) {
        alert('Please select either a workspace or an agent, not both.');
        return;
      }
      if (!this.promptData.workspaceName && !this.promptData.agentName) {
        alert('Please select either a workspace or an agent.');
        return;
      }
      this.modalRef.close(this.promptData);
    }
  }

  onCancel() {
    this.modalRef.close();
  }
}
