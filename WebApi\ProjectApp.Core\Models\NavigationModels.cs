using System.Collections.Generic;

namespace ProjectApp.Core.Models
{
    public class NavigationItem
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // agent, workspace, settings, plugin, notes, etc.
        public string Category { get; set; } = string.Empty;
        public string SubCategory { get; set; } = string.Empty;
        public string Route { get; set; } = string.Empty;
        public string[] Keywords { get; set; } = new string[0];
    }

    public class NavigationResult
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string NavigationType { get; set; } = string.Empty; // agent, workspace, settings, plugin, notes, etc.
        public string Route { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
    }
}
