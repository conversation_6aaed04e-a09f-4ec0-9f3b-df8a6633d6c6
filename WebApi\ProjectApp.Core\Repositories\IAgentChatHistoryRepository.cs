using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;

namespace ProjectApp.Core.Repositories
{
    public interface IAgentChatHistoryRepository
    {
        Task<AgentChatConversationDto> GetHistoryAsync(string agentName);
        Task<PaginatedAgentChatConversationDto> GetHistoryPaginatedAsync(string agentName, int pageNumber = 1, int pageSize = 5);
        Task<AgentChatHistoryDto> GetHistoryById(Guid id);        
        Task<AgentChatResponse> CreateResponseAsync(CreateAgentChatResponseDto createDto);
        Task<AgentChatHistory> SaveAgentChatMessage(string question, string agentName, string responseText, List<ChatSource> chatSource);
        Task<AgentChatHistory> SaveAgentChatMessage(string question, string agentName, string responseText, List<ChatSource> chatSource, string userEmail);
        Task<List<AgentChatHistoryDto>> GetRecentConversationContext(string userEmail, string chatType, string identifier, int maxMessages = 5);
        Task<AgentChatHistory> CreateHistoryAsync(AgentChatRequestDto createDto, string userEmail);
        Task<List<string>> GetFormattedConversationContext(string userEmail, string agentName, int maxMessages = 3);
        Task<AgentChatResponse> CreateResponseWithMemoryUpdateAsync(CreateAgentChatResponseDto createDto, string question, string agentName, string userEmail);
        Task<ResponseMessageList> GetAllAgentsWithChatHistoryAsync(string userEmail);
        Task<AgentChatConversationDto> GetWorkspaceHistoryAsync(string workspaceName, string userEmail);
        Task<PaginatedAgentChatConversationDto> GetWorkspaceHistoryPaginatedAsync(string workspaceName, string userEmail, int pageNumber = 1, int pageSize = 5);
        Task<AgentChatHistory> CreateWorkspaceChatHistoryAsync(string question, string workspaceName, string selectedAgentName, string userEmail);
        Task<List<string>> GetFormattedWorkspaceConversationContext(string userEmail, string workspaceName, int maxMessages = 3);
        Task<AgentChatResponse> CreateWorkspaceResponseWithMemoryUpdateAsync(CreateAgentChatResponseDto createDto, string question, string workspaceName, string userEmail);
        Task<List<string>> GetWorkspaceAgentsAsync(string workspaceName);
        Task<ResponseMessageList> GetAllWorkspacesWithChatHistoryAsync(string userEmail);
        Task<string> GetOriginalAgentNameForHistory(Guid historyId);

        // Admin Methods
        Task<PaginatedAgentChatConversationDto> GetUserChatHistoryPaginatedAsync(string userEmail, string chatType, string identifier, int pageNumber = 1, int pageSize = 5);
    }
}
