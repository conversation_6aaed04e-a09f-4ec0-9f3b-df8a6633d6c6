using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IPromptLibraryRepository
    {
        Task<List<PromptLibraryResponseDto>> GetAllPromptsAsync(string workspaceName, string agentName, string userEmail);
        Task<PromptLibraryResponseDto> GetPromptByIdAsync(int id);
        Task<PromptLibraryResponseDto> CreateOrUpdatePromptAsync(CreateOrUpdatePromptDto prompt);
        Task<bool> DeletePromptAsync(int id, string userEmail);
        Task<bool> IncrementUsageCountAsync(int id);
    }
}