using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PromptLibraryController : ControllerBase
    {
        private readonly IPromptLibraryRepository _promptLibraryRepository;

        public PromptLibraryController(IPromptLibraryRepository promptLibraryRepository)
        {
            _promptLibraryRepository = promptLibraryRepository;
        }

        [HttpGet("GetAll")]
        public async Task<ActionResult<List<PromptLibraryResponseDto>>> GetAll(string workspaceName = null, string agentName = null, string userEmail = null)
        {
            var prompts = await _promptLibraryRepository.GetAllPromptsAsync(workspaceName, agentName, userEmail);
            return Ok(prompts);
        }

        [HttpGet("GetById/{id}")]
        public async Task<ActionResult<PromptLibraryResponseDto>> GetById(int id)
        {
            var prompt = await _promptLibraryRepository.GetPromptByIdAsync(id);
            if (prompt == null)
                return NotFound(new ResponseMessage { IsError = true, Message = "Prompt not found" });

            return Ok(prompt);
        }

        [HttpPost("CreateOrUpdate")]
        public async Task<ActionResult<PromptLibraryResponseDto>> CreateOrUpdate(CreateOrUpdatePromptDto prompt)
        {
            var result = await _promptLibraryRepository.CreateOrUpdatePromptAsync(prompt);
            return Ok(result);
        }

        [HttpDelete("Delete/{id}")]
        public async Task<ActionResult<ResponseMessage>> Delete(int id, string? userEmail = null)
        {
            var result = await _promptLibraryRepository.DeletePromptAsync(id, userEmail);
            if (!result)
                return NotFound(new ResponseMessage { IsError = true, Message = "Prompt not found or you don't have permission to delete it" });

            return Ok(new ResponseMessage { IsError = false, Message = "Prompt deleted successfully" });
        }

        [HttpPost("IncrementUsage")]
        public async Task<ActionResult<ResponseMessage>> IncrementUsage(int id)
        {
            var result = await _promptLibraryRepository.IncrementUsageCountAsync(id);
            if (!result)
                return NotFound(new ResponseMessage { IsError = true, Message = "Prompt not found" });

            return Ok(new ResponseMessage { IsError = false, Message = "Usage count incremented successfully" });
        }
    }
} 