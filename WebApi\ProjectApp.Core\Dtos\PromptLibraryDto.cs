using System;
using System.Collections.Generic;

namespace ProjectApp.Core.Dtos
{
    public class CreateOrUpdatePromptDto
    {
        public int Id { get; set; }
        public string Prompt { get; set; }
        public string ShortMessage { get; set; }
        public string WorkspaceName { get; set; }
        public string AgentName { get; set; }
        public string UserEmail { get; set; }
    }

    public class PromptLibraryResponseDto
    {
        public int Id { get; set; }
        public string Prompt { get; set; }
        public string ShortMessage { get; set; }
        public string WorkspaceName { get; set; }
        public string AgentName { get; set; }
        public int UsageCount { get; set; }
        public string UserEmail { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class PromptWorkspaceDto
    {
        public string WorkspaceName { get; set; }
        public int Count { get; set; }
    }

}