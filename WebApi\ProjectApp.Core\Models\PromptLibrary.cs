using System;

namespace ProjectApp.Core.Models
{
    public class PromptLibrary
    {
        public int Id { get; set; }
        public string Prompt { get; set; }
        public string ShortMessage { get; set; } // Short description or name for the prompt
        public string WorkspaceName { get; set; } // For workspace-specific prompts
        public string Agent<PERSON>ame { get; set; } // For agent-specific prompts
        public int UsageCount { get; set; } = 0; // To track how often a prompt is used
        public string UserEmail { get; set; } // User who created the prompt
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
    }
}